-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Workspace = _services.Workspace
local Players = _services.Players
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local BORDER_RADIUS = _design.BORDER_RADIUS
local DebugRenderer
do
	DebugRenderer = setmetatable({}, {
		__tostring = function()
			return "DebugRenderer"
		end,
	})
	DebugRenderer.__index = DebugRenderer
	function DebugRenderer.new(...)
		local self = setmetatable({}, DebugRenderer)
		return self:constructor(...) or self
	end
	function DebugRenderer:constructor()
		self.lines = {}
		self.spheres = {}
		self.texts = {}
		self.isVisible = false
		self:setupDebugFolder()
		self:setupGUI()
	end
	function DebugRenderer:show()
		self.isVisible = true
		if self.debugFolder then
			self.debugFolder.Parent = Workspace
		end
		if self.guiFolder then
			self.guiFolder.Enabled = true
		end
	end
	function DebugRenderer:hide()
		self.isVisible = false
		if self.debugFolder then
			self.debugFolder.Parent = nil
		end
		if self.guiFolder then
			self.guiFolder.Enabled = false
		end
	end
	function DebugRenderer:drawLine(from, to, color, duration)
		if color == nil then
			color = Color3.fromRGB(255, 255, 255)
		end
		if duration == nil then
			duration = 0.1
		end
		local _lines = self.lines
		local _arg0 = {
			from = from,
			to = to,
			color = color,
			duration = duration,
			createdAt = tick(),
		}
		table.insert(_lines, _arg0)
	end
	function DebugRenderer:drawSphere(position, radius, color, duration)
		if radius == nil then
			radius = 1
		end
		if color == nil then
			color = Color3.fromRGB(255, 255, 255)
		end
		if duration == nil then
			duration = 0.1
		end
		local _spheres = self.spheres
		local _arg0 = {
			position = position,
			radius = radius,
			color = color,
			duration = duration,
			createdAt = tick(),
		}
		table.insert(_spheres, _arg0)
	end
	function DebugRenderer:drawText(position, text, color, duration)
		if color == nil then
			color = Color3.fromRGB(255, 255, 255)
		end
		if duration == nil then
			duration = 0.1
		end
		local _texts = self.texts
		local _arg0 = {
			position = position,
			text = text,
			color = color,
			duration = duration,
			createdAt = tick(),
		}
		table.insert(_texts, _arg0)
	end
	function DebugRenderer:render()
		if not self.isVisible or not self.debugFolder then
			return nil
		end
		-- Clear old debug objects
		local _exp = self.debugFolder:GetChildren()
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(child)
			return child:Destroy()
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
		local currentTime = tick()
		-- Render lines
		local _exp_1 = self.lines
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback_1 = function(line)
			if currentTime - line.createdAt > line.duration then
				return false
			end
			self:createLinePart(line)
			return true
		end
		local _length = 0
		for _k, _v in _exp_1 do
			if _callback_1(_v, _k - 1, _exp_1) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		self.lines = _newValue
		-- Render spheres
		local _exp_2 = self.spheres
		-- ▼ ReadonlyArray.filter ▼
		local _newValue_1 = {}
		local _callback_2 = function(sphere)
			if currentTime - sphere.createdAt > sphere.duration then
				return false
			end
			self:createSpherePart(sphere)
			return true
		end
		local _length_1 = 0
		for _k, _v in _exp_2 do
			if _callback_2(_v, _k - 1, _exp_2) == true then
				_length_1 += 1
				_newValue_1[_length_1] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		self.spheres = _newValue_1
		-- Render 3D text
		local _exp_3 = self.texts
		-- ▼ ReadonlyArray.filter ▼
		local _newValue_2 = {}
		local _callback_3 = function(text)
			if currentTime - text.createdAt > text.duration then
				return false
			end
			self:create3DText(text)
			return true
		end
		local _length_2 = 0
		for _k, _v in _exp_3 do
			if _callback_3(_v, _k - 1, _exp_3) == true then
				_length_2 += 1
				_newValue_2[_length_2] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		self.texts = _newValue_2
	end
	function DebugRenderer:setupDebugFolder()
		self.debugFolder = Instance.new("Folder")
		self.debugFolder.Name = "DebugVisuals"
		-- Don't parent it yet - will be parented when shown
	end
	function DebugRenderer:setupGUI()
		local player = Players.LocalPlayer
		if not player then
			return nil
		end
		local playerGui = player:WaitForChild("PlayerGui")
		self.guiFolder = Instance.new("ScreenGui")
		self.guiFolder.Name = "DebugGUI"
		self.guiFolder.ResetOnSpawn = false
		self.guiFolder.Enabled = false
		self.guiFolder.Parent = playerGui
	end
	function DebugRenderer:createLinePart(line)
		if not self.debugFolder then
			return nil
		end
		local _from = line.from
		local _to = line.to
		local distance = (_from - _to).Magnitude
		local _from_1 = line.from
		local _to_1 = line.to
		local midpoint = (_from_1 + _to_1) / 2
		local _to_2 = line.to
		local _from_2 = line.from
		local direction = (_to_2 - _from_2).Unit
		local part = Instance.new("Part")
		part.Name = "DebugLine"
		part.Size = Vector3.new(0.1, 0.1, distance)
		part.Material = Enum.Material.Neon
		part.Color = line.color
		part.Anchored = true
		part.CanCollide = false
		part.Transparency = 0.3
		part.CFrame = CFrame.lookAt(midpoint, midpoint + direction)
		part.Parent = self.debugFolder
	end
	function DebugRenderer:createSpherePart(sphere)
		if not self.debugFolder then
			return nil
		end
		local part = Instance.new("Part")
		part.Name = "DebugSphere"
		part.Shape = Enum.PartType.Ball
		part.Size = Vector3.new(sphere.radius * 2, sphere.radius * 2, sphere.radius * 2)
		part.Material = Enum.Material.ForceField
		part.Color = sphere.color
		part.Position = sphere.position
		part.Anchored = true
		part.CanCollide = false
		part.Transparency = 0.7
		part.Parent = self.debugFolder
	end
	function DebugRenderer:create3DText(text)
		if not self.debugFolder then
			return nil
		end
		-- Create a part to hold the text
		local part = Instance.new("Part")
		part.Name = "DebugText"
		part.Size = Vector3.new(0.1, 0.1, 0.1)
		part.Position = text.position
		part.Anchored = true
		part.CanCollide = false
		part.Transparency = 1
		part.Parent = self.debugFolder
		-- Create BillboardGui for 3D text
		local billboard = Instance.new("BillboardGui")
		billboard.Size = UDim2.fromOffset(200, 50)
		billboard.StudsOffset = Vector3.new(0, 2, 0)
		billboard.Parent = part
		-- Create text label
		local label = Instance.new("TextLabel")
		label.Size = UDim2.fromScale(1, 1)
		label.BackgroundTransparency = 1
		label.Text = text.text
		label.TextColor3 = text.color
		label.TextScaled = true
		label.Font = Enum.Font.SourceSansBold
		label.TextStrokeTransparency = 0
		label.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
		label.Parent = billboard
	end
	function DebugRenderer:createGUIElement(name, position, size, text)
		if not self.guiFolder then
			return nil
		end
		-- Remove existing element with same name to prevent overlapping
		local existingFrame = self.guiFolder:FindFirstChild(name .. "Frame")
		if existingFrame then
			existingFrame:Destroy()
		end
		local frame = Instance.new("Frame")
		frame.Name = name .. "Frame"
		frame.Position = position
		frame.Size = size
		frame.BackgroundColor3 = Color3.fromHex(COLORS.bg.base)
		frame.BackgroundTransparency = 0.05
		frame.BorderSizePixel = 0
		frame.ZIndex = 10
		frame.Parent = self.guiFolder
		-- Add corner radius using design system
		local corner = Instance.new("UICorner")
		corner.CornerRadius = UDim.new(0, BORDER_RADIUS.md)
		corner.Parent = frame
		-- Add border using design system
		local stroke = Instance.new("UIStroke")
		stroke.Color = Color3.fromHex(COLORS.border.l2)
		stroke.Thickness = 1
		stroke.Parent = frame
		-- Add proper padding using design system
		local padding = Instance.new("UIPadding")
		padding.PaddingTop = UDim.new(0, 12)
		padding.PaddingBottom = UDim.new(0, 12)
		padding.PaddingLeft = UDim.new(0, 16)
		padding.PaddingRight = UDim.new(0, 16)
		padding.Parent = frame
		local label = Instance.new("TextLabel")
		label.Name = name .. "Label"
		label.Size = UDim2.fromScale(1, 1)
		label.BackgroundTransparency = 1
		label.Text = text
		label.TextColor3 = Color3.fromHex(COLORS.text.main)
		label.TextSize = 14
		label.Font = Enum.Font.RobotoMono
		label.TextXAlignment = Enum.TextXAlignment.Left
		label.TextYAlignment = Enum.TextYAlignment.Top
		label.TextWrapped = false
		label.ZIndex = 11
		label.Parent = frame
		return label
	end
	function DebugRenderer:updateGUIElement(name, text)
		if not self.guiFolder then
			return nil
		end
		local label = self.guiFolder:FindFirstChild(name .. "Label")
		if label then
			label.Text = text
		end
	end
	function DebugRenderer:drawPath(points, color, duration)
		if color == nil then
			color = Color3.fromRGB(255, 255, 0)
		end
		if duration == nil then
			duration = 0.1
		end
		for i = 0, #points - 2 do
			self:drawLine(points[i + 1], points[i + 2], color, duration)
		end
		-- Draw waypoint markers
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(point, index)
			self:drawSphere(point, 0.5, color, duration)
			self:drawText(point, `{index}`, color, duration)
		end
		for _k, _v in points do
			_callback(_v, _k - 1, points)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	function DebugRenderer:drawVisionCone(position, direction, range, angle, color, duration)
		if color == nil then
			color = Color3.fromRGB(0, 255, 0)
		end
		if duration == nil then
			duration = 0.1
		end
		local angleRad = math.rad(angle / 2)
		local steps = 8
		-- Draw vision cone lines
		for i = 0, steps do
			local currentAngle = -angleRad + (angleRad * 2 * i / steps)
			local _direction = direction
			local _range = range
			local rotatedDirection = _direction * _range
			-- Simple 2D rotation around Y axis
			local x = rotatedDirection.X * math.cos(currentAngle) - rotatedDirection.Z * math.sin(currentAngle)
			local z = rotatedDirection.X * math.sin(currentAngle) + rotatedDirection.Z * math.cos(currentAngle)
			local _position = position
			local _vector3 = Vector3.new(x, rotatedDirection.Y, z)
			local endPoint = _position + _vector3
			self:drawLine(position, endPoint, color, duration)
		end
		-- Draw arc at the end
		for i = 0, steps - 1 do
			local angle1 = -angleRad + (angleRad * 2 * i / steps)
			local angle2 = -angleRad + (angleRad * 2 * (i + 1) / steps)
			local x1 = direction.X * range * math.cos(angle1) - direction.Z * range * math.sin(angle1)
			local z1 = direction.X * range * math.sin(angle1) + direction.Z * range * math.cos(angle1)
			local _position = position
			local _vector3 = Vector3.new(x1, direction.Y * range, z1)
			local point1 = _position + _vector3
			local x2 = direction.X * range * math.cos(angle2) - direction.Z * range * math.sin(angle2)
			local z2 = direction.X * range * math.sin(angle2) + direction.Z * range * math.cos(angle2)
			local _position_1 = position
			local _vector3_1 = Vector3.new(x2, direction.Y * range, z2)
			local point2 = _position_1 + _vector3_1
			self:drawLine(point1, point2, color, duration)
		end
	end
	function DebugRenderer:cleanup()
		self.lines = {}
		self.spheres = {}
		self.texts = {}
		if self.debugFolder then
			self.debugFolder:Destroy()
		end
		if self.guiFolder then
			self.guiFolder:Destroy()
		end
	end
end
return {
	DebugRenderer = DebugRenderer,
}
