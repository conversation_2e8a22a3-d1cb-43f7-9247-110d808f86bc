-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Players = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Players
local PlayerDebugger
do
	PlayerDebugger = setmetatable({}, {
		__tostring = function()
			return "PlayerDebugger"
		end,
	})
	PlayerDebugger.__index = PlayerDebugger
	function PlayerDebugger.new(...)
		local self = setmetatable({}, PlayerDebugger)
		return self:constructor(...) or self
	end
	function PlayerDebugger:constructor(renderer)
		self.renderer = renderer
		self:setupGUI()
	end
	function PlayerDebugger:update(config)
		local players = Players:GetPlayers()
		-- Update player info display
		self:updatePlayerInfo(#players)
		-- Debug each player
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(player, index)
			self:debugPlayer(player, config, index)
		end
		for _k, _v in players do
			_callback(_v, _k - 1, players)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	function PlayerDebugger:setupGUI()
		local _renderer = self.renderer
		local _exp = UDim2.fromScale(1, 1)
		local _arg0 = UDim2.fromOffset(385, 420)
		self.playerInfoLabel = _renderer:createGUIElement("PlayerInfo", _exp - _arg0, UDim2.fromOffset(370, 200), "Player Debug Info")
	end
	function PlayerDebugger:updatePlayerInfo(playerCount)
		if not self.playerInfoLabel then
			return nil
		end
		local localPlayer = Players.LocalPlayer
		local _exp = `Total Players: {playerCount}`
		local _result = localPlayer
		if _result ~= nil then
			_result = _result.Name
		end
		local _condition = _result
		if not (_condition ~= "" and _condition) then
			_condition = "None"
		end
		local info = { "=== PLAYER DEBUG INFO ===", _exp, `Local Player: {_condition}`, "" }
		-- Add individual player info
		local _exp_1 = Players:GetPlayers()
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(player, index)
			if player.Character and player.Character.PrimaryPart then
				local pos = player.Character.PrimaryPart.Position
				local humanoid = player.Character:FindFirstChild("Humanoid")
				local health = if humanoid then `{math.floor(humanoid.Health)}/{math.floor(humanoid.MaxHealth)}` else "N/A"
				local walkSpeed = if humanoid then math.floor(humanoid.WalkSpeed) else "N/A"
				local _arg0 = `{player.Name}:`
				table.insert(info, _arg0)
				local _arg0_1 = `  Pos: ({math.floor(pos.X)}, {math.floor(pos.Y)}, {math.floor(pos.Z)})`
				table.insert(info, _arg0_1)
				local _arg0_2 = `  Health: {health}`
				table.insert(info, _arg0_2)
				local _arg0_3 = `  Speed: {walkSpeed}`
				table.insert(info, _arg0_3)
				table.insert(info, "")
			else
				local _arg0 = `{player.Name}: No Character`
				table.insert(info, _arg0)
				table.insert(info, "")
			end
		end
		for _k, _v in _exp_1 do
			_callback(_v, _k - 1, _exp_1)
		end
		-- ▲ ReadonlyArray.forEach ▲
		self.playerInfoLabel.Text = table.concat(info, "\n")
	end
	function PlayerDebugger:debugPlayer(player, config, index)
		if not player.Character or not player.Character.PrimaryPart then
			return nil
		end
		local character = player.Character
		local humanoidRootPart = character.PrimaryPart
		local position = humanoidRootPart.Position
		local humanoid = character:FindFirstChild("Humanoid")
		-- Show player position and basic info
		if config.showPositions then
			local isLocalPlayer = player == Players.LocalPlayer
			local playerColor = if isLocalPlayer then Color3.fromRGB(0, 255, 0) else Color3.fromRGB(0, 0, 255)
			self.renderer:drawSphere(position, 1.5, playerColor, 0.1)
			local _exp = { `Player: {player.Name}`, `Pos: {math.floor(position.X)}, {math.floor(position.Y)}, {math.floor(position.Z)}`, if humanoid then `Health: {math.floor(humanoid.Health)}/{math.floor(humanoid.MaxHealth)}` else "", if humanoid then `Speed: {math.floor(humanoid.WalkSpeed)}` else "" }
			-- ▼ ReadonlyArray.filter ▼
			local _newValue = {}
			local _callback = function(line)
				return line ~= ""
			end
			local _length = 0
			for _k, _v in _exp do
				if _callback(_v, _k - 1, _exp) == true then
					_length += 1
					_newValue[_length] = _v
				end
			end
			-- ▲ ReadonlyArray.filter ▲
			local playerInfo = table.concat(_newValue, "\n")
			local _renderer = self.renderer
			local _vector3 = Vector3.new(0, 4, 0)
			_renderer:drawText(position + _vector3, playerInfo, Color3.fromRGB(255, 255, 255), 0.1)
		end
		-- Show player movement vector
		if config.showPlayers and humanoid then
			local moveVector = humanoid.MoveDirection
			if moveVector.Magnitude > 0 then
				local _arg0 = moveVector * 5
				local endPos = position + _arg0
				self.renderer:drawLine(position, endPos, Color3.fromRGB(255, 255, 0), 0.1)
				self.renderer:drawText(endPos, "Movement", Color3.fromRGB(255, 255, 0), 0.1)
			end
		end
		-- Show player look direction
		if config.showPlayers then
			local lookDirection = humanoidRootPart.CFrame.LookVector
			local _arg0 = lookDirection * 3
			local lookEndPos = position + _arg0
			self.renderer:drawLine(position, lookEndPos, Color3.fromRGB(255, 0, 0), 0.1)
		end
		-- Show player's interaction range
		if config.showPlayers then
			self.renderer:drawSphere(position, 5, Color3.fromRGB(0, 255, 255), 0.1)
		end
		-- Debug player's body parts (if enabled)
		if config.showPositions then
			self:debugPlayerBodyParts(character)
		end
		-- Show player connections/relationships
		self:debugPlayerRelationships(player, position, config)
	end
	function PlayerDebugger:debugPlayerBodyParts(character)
		local bodyParts = { "Head", "Torso", "HumanoidRootPart", "Left Arm", "Right Arm", "Left Leg", "Right Leg" }
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(partName)
			local part = character:FindFirstChild(partName)
			if part then
				-- Draw small markers for body parts
				self.renderer:drawSphere(part.Position, 0.3, Color3.fromRGB(255, 255, 255), 0.1)
				local _renderer = self.renderer
				local _position = part.Position
				local _vector3 = Vector3.new(0, 1, 0)
				_renderer:drawText(_position + _vector3, partName, Color3.fromRGB(200, 200, 200), 0.1)
			end
		end
		for _k, _v in bodyParts do
			_callback(_v, _k - 1, bodyParts)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	function PlayerDebugger:debugPlayerRelationships(player, position, config)
		if not config.showPlayers then
			return nil
		end
		-- Show distance to other players
		local _exp = Players:GetPlayers()
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(otherPlayer)
			if otherPlayer == player or not otherPlayer.Character or not otherPlayer.Character.PrimaryPart then
				return nil
			end
			local otherPosition = otherPlayer.Character.PrimaryPart.Position
			local distance = (position - otherPosition).Magnitude
			-- Only show if players are relatively close
			if distance < 50 then
				local midpoint = (position + otherPosition) / 2
				local distanceText = `{math.floor(distance)} studs`
				-- Color based on distance (green = close, red = far)
				local normalizedDistance = math.min(distance / 50, 1)
				local lineColor = Color3.fromRGB(math.floor(255 * normalizedDistance), math.floor(255 * (1 - normalizedDistance)), 0)
				self.renderer:drawLine(position, otherPosition, lineColor, 0.1)
				self.renderer:drawText(midpoint, distanceText, lineColor, 0.1)
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	function PlayerDebugger:debugPlayerEquipment(player, position)
		if not player.Character then
			return nil
		end
		local tool = player.Character:FindFirstChildOfClass("Tool")
		if tool then
			local toolInfo = `Tool: {tool.Name}`
			local _renderer = self.renderer
			local _position = position
			local _vector3 = Vector3.new(0, -2, 0)
			_renderer:drawText(_position + _vector3, toolInfo, Color3.fromRGB(255, 255, 0), 0.1)
		end
	end
	function PlayerDebugger:debugPlayerAnimations(player, position)
		if not player.Character then
			return nil
		end
		local humanoid = player.Character:FindFirstChild("Humanoid")
		if not humanoid then
			return nil
		end
		-- Get current animation tracks (this is simplified - actual implementation would be more complex)
		local animationInfo = table.concat({ `Jump: {if humanoid.Jump then "YES" else "NO"}`, `Sit: {if humanoid.Sit then "YES" else "NO"}`, `PlatformStand: {if humanoid.PlatformStand then "YES" else "NO"}` }, "\n")
		local _renderer = self.renderer
		local _position = position
		local _vector3 = Vector3.new(-3, 0, 0)
		_renderer:drawText(_position + _vector3, animationInfo, Color3.fromRGB(200, 200, 255), 0.1)
	end
	function PlayerDebugger:cleanup()
		-- Cleanup any resources
	end
end
return {
	PlayerDebugger = PlayerDebugger,
}
