import { Workspace, Players } from "@rbxts/services";
import { COLORS, BORDER_RADIUS } from "../design";
import { ZIndexManager } from "../gui/layout/ZIndexManager";

export interface DebugLine {
	from: Vector3;
	to: Vector3;
	color: Color3;
	duration: number;
	createdAt: number;
}

export interface DebugSphere {
	position: Vector3;
	radius: number;
	color: Color3;
	duration: number;
	createdAt: number;
}

export interface DebugText {
	position: Vector3;
	text: string;
	color: Color3;
	duration: number;
	createdAt: number;
}

export class DebugRenderer {
	private lines: DebugLine[] = [];
	private spheres: DebugSphere[] = [];
	private texts: DebugText[] = [];
	private debugFolder?: Folder;
	private guiFolder?: ScreenGui;
	private isVisible = false;

	constructor() {
		this.setupDebugFolder();
		this.setupGUI();
	}

	public show(): void {
		this.isVisible = true;
		if (this.debugFolder) {
			this.debugFolder.Parent = Workspace;
		}
		if (this.guiFolder) {
			this.guiFolder.Enabled = true;
		}
	}

	public hide(): void {
		this.isVisible = false;
		if (this.debugFolder) {
			this.debugFolder.Parent = undefined;
		}
		if (this.guiFolder) {
			this.guiFolder.Enabled = false;
		}
	}

	public drawLine(from: Vector3, to: Vector3, color = Color3.fromRGB(255, 255, 255), duration = 0.1): void {
		this.lines.push({
			from,
			to,
			color,
			duration,
			createdAt: tick()
		});
	}

	public drawSphere(position: Vector3, radius = 1, color = Color3.fromRGB(255, 255, 255), duration = 0.1): void {
		this.spheres.push({
			position,
			radius,
			color,
			duration,
			createdAt: tick()
		});
	}

	public drawText(position: Vector3, text: string, color = Color3.fromRGB(255, 255, 255), duration = 0.1): void {
		this.texts.push({
			position,
			text,
			color,
			duration,
			createdAt: tick()
		});
	}

	public render(): void {
		if (!this.isVisible || !this.debugFolder) return;

		// Clear old debug objects
		this.debugFolder.GetChildren().forEach(child => child.Destroy());

		const currentTime = tick();

		// Render lines
		this.lines = this.lines.filter(line => {
			if (currentTime - line.createdAt > line.duration) {
				return false;
			}

			this.createLinePart(line);
			return true;
		});

		// Render spheres
		this.spheres = this.spheres.filter(sphere => {
			if (currentTime - sphere.createdAt > sphere.duration) {
				return false;
			}

			this.createSpherePart(sphere);
			return true;
		});

		// Render 3D text
		this.texts = this.texts.filter(text => {
			if (currentTime - text.createdAt > text.duration) {
				return false;
			}

			this.create3DText(text);
			return true;
		});
	}

	private setupDebugFolder(): void {
		this.debugFolder = new Instance("Folder");
		this.debugFolder.Name = "DebugVisuals";
		// Don't parent it yet - will be parented when shown
	}

	private setupGUI(): void {
		const player = Players.LocalPlayer;
		if (!player) return;

		const playerGui = player.WaitForChild("PlayerGui") as PlayerGui;
		
		this.guiFolder = new Instance("ScreenGui");
		this.guiFolder.Name = "DebugGUI";
		this.guiFolder.ResetOnSpawn = false;
		this.guiFolder.Enabled = false;
		this.guiFolder.Parent = playerGui;
	}

	private createLinePart(line: DebugLine): void {
		if (!this.debugFolder) return;

		const distance = line.from.sub(line.to).Magnitude;
		const midpoint = line.from.add(line.to).div(2);
		const direction = line.to.sub(line.from).Unit;

		const part = new Instance("Part");
		part.Name = "DebugLine";
		part.Size = new Vector3(0.1, 0.1, distance);
		part.Material = Enum.Material.Neon;
		part.Color = line.color;
		part.Anchored = true;
		part.CanCollide = false;
		part.Transparency = 0.3;
		part.CFrame = CFrame.lookAt(midpoint, midpoint.add(direction));
		part.Parent = this.debugFolder;
	}

	private createSpherePart(sphere: DebugSphere): void {
		if (!this.debugFolder) return;

		const part = new Instance("Part");
		part.Name = "DebugSphere";
		part.Shape = Enum.PartType.Ball;
		part.Size = new Vector3(sphere.radius * 2, sphere.radius * 2, sphere.radius * 2);
		part.Material = Enum.Material.ForceField;
		part.Color = sphere.color;
		part.Position = sphere.position;
		part.Anchored = true;
		part.CanCollide = false;
		part.Transparency = 0.7;
		part.Parent = this.debugFolder;
	}

	private create3DText(text: DebugText): void {
		if (!this.debugFolder) return;

		// Create a part to hold the text
		const part = new Instance("Part");
		part.Name = "DebugText";
		part.Size = new Vector3(0.1, 0.1, 0.1);
		part.Position = text.position;
		part.Anchored = true;
		part.CanCollide = false;
		part.Transparency = 1;
		part.Parent = this.debugFolder;

		// Create BillboardGui for 3D text
		const billboard = new Instance("BillboardGui");
		billboard.Size = UDim2.fromOffset(200, 50);
		billboard.StudsOffset = new Vector3(0, 2, 0);
		billboard.Parent = part;

		// Create text label
		const label = new Instance("TextLabel");
		label.Size = UDim2.fromScale(1, 1);
		label.BackgroundTransparency = 1;
		label.Text = text.text;
		label.TextColor3 = text.color;
		label.TextScaled = true;
		label.Font = Enum.Font.SourceSansBold;
		label.TextStrokeTransparency = 0;
		label.TextStrokeColor3 = Color3.fromRGB(0, 0, 0);
		label.Parent = billboard;
	}

	public createGUIElement(name: string, position: UDim2, size: UDim2, text: string): TextLabel | undefined {
		if (!this.guiFolder) return undefined;

		// Remove existing element with same name to prevent overlapping
		const existingFrame = this.guiFolder.FindFirstChild(name + "Frame");
		if (existingFrame) {
			existingFrame.Destroy();
		}

		const frame = new Instance("Frame");
		frame.Name = name + "Frame";
		frame.Position = position;
		frame.Size = size;
		frame.BackgroundColor3 = Color3.fromHex(COLORS.bg.base);
		frame.BackgroundTransparency = 0.05;
		frame.BorderSizePixel = 0;
		// Use regular ZIndexManager for debug overlay panels (should be behind interactive panels)
		frame.ZIndex = ZIndexManager.getNextZIndex(`debug-${name.lower()}`);
		frame.Parent = this.guiFolder;

		// Add corner radius using design system
		const corner = new Instance("UICorner");
		corner.CornerRadius = new UDim(0, BORDER_RADIUS.md);
		corner.Parent = frame;

		// Add border using design system
		const stroke = new Instance("UIStroke");
		stroke.Color = Color3.fromHex(COLORS.border.l2);
		stroke.Thickness = 1;
		stroke.Parent = frame;

		// Add proper padding using design system
		const padding = new Instance("UIPadding");
		padding.PaddingTop = new UDim(0, 12);
		padding.PaddingBottom = new UDim(0, 12);
		padding.PaddingLeft = new UDim(0, 16);
		padding.PaddingRight = new UDim(0, 16);
		padding.Parent = frame;

		const label = new Instance("TextLabel");
		label.Name = name + "Label";
		label.Size = UDim2.fromScale(1, 1);
		label.BackgroundTransparency = 1;
		label.Text = text;
		label.TextColor3 = Color3.fromHex(COLORS.text.main);
		label.TextSize = 14;
		label.Font = Enum.Font.RobotoMono; // Use monospace font for better alignment
		label.TextXAlignment = Enum.TextXAlignment.Left;
		label.TextYAlignment = Enum.TextYAlignment.Top;
		label.TextWrapped = false; // Don't wrap debug text
		label.ZIndex = frame.ZIndex + 1; // Ensure text is above the frame
		label.Parent = frame;

		return label;
	}

	public updateGUIElement(name: string, text: string): void {
		if (!this.guiFolder) return;

		const label = this.guiFolder.FindFirstChild(name + "Label") as TextLabel;
		if (label) {
			label.Text = text;
		}
	}

	public drawPath(points: Vector3[], color = Color3.fromRGB(255, 255, 0), duration = 0.1): void {
		for (let i = 0; i < points.size() - 1; i++) {
			this.drawLine(points[i], points[i + 1], color, duration);
		}

		// Draw waypoint markers
		points.forEach((point, index) => {
			this.drawSphere(point, 0.5, color, duration);
			this.drawText(point, `${index}`, color, duration);
		});
	}

	public drawVisionCone(position: Vector3, direction: Vector3, range: number, angle: number, color = Color3.fromRGB(0, 255, 0), duration = 0.1): void {
		const angleRad = math.rad(angle / 2);
		const steps = 8;

		// Draw vision cone lines
		for (let i = 0; i <= steps; i++) {
			const currentAngle = -angleRad + (angleRad * 2 * i / steps);
			const rotatedDirection = direction.mul(range);

			// Simple 2D rotation around Y axis
			const x = rotatedDirection.X * math.cos(currentAngle) - rotatedDirection.Z * math.sin(currentAngle);
			const z = rotatedDirection.X * math.sin(currentAngle) + rotatedDirection.Z * math.cos(currentAngle);
			const endPoint = position.add(new Vector3(x, rotatedDirection.Y, z));

			this.drawLine(position, endPoint, color, duration);
		}

		// Draw arc at the end
		for (let i = 0; i < steps; i++) {
			const angle1 = -angleRad + (angleRad * 2 * i / steps);
			const angle2 = -angleRad + (angleRad * 2 * (i + 1) / steps);

			const x1 = direction.X * range * math.cos(angle1) - direction.Z * range * math.sin(angle1);
			const z1 = direction.X * range * math.sin(angle1) + direction.Z * range * math.cos(angle1);
			const point1 = position.add(new Vector3(x1, direction.Y * range, z1));

			const x2 = direction.X * range * math.cos(angle2) - direction.Z * range * math.sin(angle2);
			const z2 = direction.X * range * math.sin(angle2) + direction.Z * range * math.cos(angle2);
			const point2 = position.add(new Vector3(x2, direction.Y * range, z2));

			this.drawLine(point1, point2, color, duration);
		}
	}

	public cleanup(): void {
		this.lines = [];
		this.spheres = [];
		this.texts = [];

		if (this.debugFolder) {
			this.debugFolder.Destroy();
		}

		if (this.guiFolder) {
			this.guiFolder.Destroy();
		}
	}
}
