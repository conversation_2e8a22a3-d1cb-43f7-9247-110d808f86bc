import * as React from "@rbxts/react";
export interface BaseFrameProps {
    children?: React.ReactNode;
    size?: UDim2;
    position?: UDim2;
    anchorPoint?: Vector2;
    layoutOrder?: number;
    backgroundColor?: string;
    backgroundTransparency?: number;
    padding?: number;
    zIndex?: number;
    autoSize?: Enum.AutomaticSize;
    fillDirection?: "auto" | "manual";
}
export interface VerticalFrameProps extends BaseFrameProps {
    spacing?: number;
    horizontalAlignment?: Enum.HorizontalAlignment;
    fitContent?: boolean;
}
export interface HorizontalFrameProps extends BaseFrameProps {
    spacing?: number;
    horizontalAlignment?: Enum.HorizontalAlignment;
    verticalAlignment?: Enum.VerticalAlignment;
    fitContent?: boolean;
}
export interface ContainerFrameProps extends BaseFrameProps {
    cornerRadius?: number;
    borderColor?: string;
    borderThickness?: number;
    borderTransparency?: number;
    fitContent?: boolean;
}
export interface ScrollingFrameProps extends BaseFrameProps {
    canvasSize?: UDim2;
    scrollBarThickness?: number;
    borderColor?: string;
    borderThickness?: number;
    cornerRadius?: number;
    scrollingDirection?: Enum.ScrollingDirection;
    elasticBehavior?: Enum.ElasticBehavior;
    automaticCanvasSize?: Enum.AutomaticSize;
}
