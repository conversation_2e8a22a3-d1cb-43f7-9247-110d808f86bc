-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local RunService = _services.RunService
local Players = _services.Players
local ReplicatedStorage = _services.ReplicatedStorage
local PerformanceMonitor
do
	PerformanceMonitor = setmetatable({}, {
		__tostring = function()
			return "PerformanceMonitor"
		end,
	})
	PerformanceMonitor.__index = PerformanceMonitor
	function PerformanceMonitor.new(...)
		local self = setmetatable({}, PerformanceMonitor)
		return self:constructor(...) or self
	end
	function PerformanceMonitor:constructor(renderer)
		self.fpsHistory = {}
		self.frameTimeHistory = {}
		self.lastUpdateTime = 0
		self.updateInterval = 0.5
		self.maxHistorySize = 60
		self.renderer = renderer
		self:setupGUI()
	end
	function PerformanceMonitor:update(deltaTime)
		local currentTime = tick()
		-- Only update at specified intervals
		if currentTime - self.lastUpdateTime < self.updateInterval then
			return nil
		end
		self.lastUpdateTime = currentTime
		local perfData = self:collectPerformanceData(deltaTime)
		self:updateHistory(perfData)
		self:updatePerformanceDisplay(perfData)
	end
	function PerformanceMonitor:setupGUI()
		local _renderer = self.renderer
		local _exp = UDim2.fromScale(1, 0)
		local _arg0 = UDim2.fromOffset(360, 0)
		self.performanceLabel = _renderer:createGUIElement("Performance", _exp - _arg0, UDim2.fromOffset(340, 180), "Performance Monitor")
	end
	function PerformanceMonitor:collectPerformanceData(deltaTime)
		-- Calculate FPS
		local fps = math.floor(1 / deltaTime)
		-- Get frame time in milliseconds
		local frameTime = deltaTime * 1000
		-- Get memory usage (approximation)
		local memoryUsage = self:getMemoryUsage()
		-- Get network stats (if available)
		local networkStats = self:getNetworkStats()
		-- Get instance count
		local instanceCount = self:getInstanceCount()
		-- Get heartbeat time
		local heartbeatTime = self:getHeartbeatTime()
		return {
			fps = fps,
			frameTime = frameTime,
			memoryUsage = memoryUsage,
			networkReceive = networkStats.receive,
			networkSend = networkStats.send,
			instanceCount = instanceCount,
			heartbeatTime = heartbeatTime,
		}
	end
	function PerformanceMonitor:updateHistory(perfData)
		-- Update FPS history
		local _fpsHistory = self.fpsHistory
		local _fps = perfData.fps
		table.insert(_fpsHistory, _fps)
		if #self.fpsHistory > self.maxHistorySize then
			table.remove(self.fpsHistory, 1)
		end
		-- Update frame time history
		local _frameTimeHistory = self.frameTimeHistory
		local _frameTime = perfData.frameTime
		table.insert(_frameTimeHistory, _frameTime)
		if #self.frameTimeHistory > self.maxHistorySize then
			table.remove(self.frameTimeHistory, 1)
		end
	end
	function PerformanceMonitor:updatePerformanceDisplay(perfData)
		if not self.performanceLabel then
			return nil
		end
		local avgFPS = self:calculateAverage(self.fpsHistory)
		local minFPS = if #self.fpsHistory > 0 then math.min(unpack(self.fpsHistory)) else 0
		local maxFPS = if #self.fpsHistory > 0 then math.max(unpack(self.fpsHistory)) else 0
		local avgFrameTime = self:calculateAverage(self.frameTimeHistory)
		local minFrameTime = if #self.frameTimeHistory > 0 then math.min(unpack(self.frameTimeHistory)) else 0
		local maxFrameTime = if #self.frameTimeHistory > 0 then math.max(unpack(self.frameTimeHistory)) else 0
		local info = { "=== PERFORMANCE MONITOR ===", "", "FRAME RATE:", `  Current: {perfData.fps} FPS`, `  Average: {math.floor(avgFPS)} FPS`, `  Min/Max: {minFPS}/{maxFPS} FPS`, "", "FRAME TIME:", `  Current: {math.floor(perfData.frameTime * 100) / 100} ms`, `  Average: {math.floor(avgFrameTime * 100) / 100} ms`, `  Min/Max: {math.floor(minFrameTime * 100) / 100}/{math.floor(maxFrameTime * 100) / 100} ms`, "", "MEMORY:", `  Usage: {math.floor(perfData.memoryUsage * 10) / 10} MB`, "", "NETWORK:", `  Receive: {math.floor(perfData.networkReceive * 10) / 10} KB/s`, `  Send: {math.floor(perfData.networkSend * 10) / 10} KB/s`, "", "SYSTEM:", `  Instances: {perfData.instanceCount}`, `  Heartbeat: {math.floor(perfData.heartbeatTime * 100) / 100} ms`, "", self:getPerformanceRating(avgFPS) }
		self.performanceLabel.Text = table.concat(info, "\n")
	end
	function PerformanceMonitor:getMemoryUsage()
		-- This is an approximation - Roblox doesn't expose direct memory usage
		-- We can estimate based on instance count and other factors
		TS.try(function()
			-- Stats is not a function in Roblox, it's a service
			-- For now, we'll use a simple estimation
		end, function()
			-- Fallback estimation
		end)
		-- Rough estimation based on instance count
		local instanceCount = self:getInstanceCount()
		return instanceCount * 0.001
	end
	function PerformanceMonitor:getNetworkStats()
		-- Placeholder - actual network stats would require more complex implementation
		return {
			receive = 0,
			send = 0,
		}
	end
	function PerformanceMonitor:getInstanceCount()
		local count = 0
		local countInstances
		countInstances = function(parent)
			count += 1
			local _exp = parent:GetChildren()
			-- ▼ ReadonlyArray.forEach ▼
			local _callback = function(child)
				countInstances(child)
			end
			for _k, _v in _exp do
				_callback(_v, _k - 1, _exp)
			end
			-- ▲ ReadonlyArray.forEach ▲
		end
		local _exitType, _returns = TS.try(function()
			countInstances(game.Workspace)
			countInstances(Players)
			countInstances(ReplicatedStorage)
		end, function()
			-- If we can't access these, return 0
			return TS.TRY_RETURN, { 0 }
		end)
		if _exitType then
			return unpack(_returns)
		end
		return count
	end
	function PerformanceMonitor:getHeartbeatTime()
		-- Measure time for a single heartbeat step
		local startTime = tick()
		RunService.Heartbeat:Wait()
		return (tick() - startTime) * 1000
	end
	function PerformanceMonitor:calculateAverage(numbers)
		if #numbers == 0 then
			return 0
		end
		-- ▼ ReadonlyArray.reduce ▼
		local _result = 0
		local _callback = function(acc, num)
			return acc + num
		end
		for _i = 1, #numbers do
			_result = _callback(_result, numbers[_i], _i - 1, numbers)
		end
		-- ▲ ReadonlyArray.reduce ▲
		local sum = _result
		return sum / #numbers
	end
	function PerformanceMonitor:getPerformanceRating(avgFPS)
		if avgFPS >= 55 then
			return "Performance: EXCELLENT ✅"
		elseif avgFPS >= 45 then
			return "Performance: GOOD ✅"
		elseif avgFPS >= 30 then
			return "Performance: FAIR ⚠️"
		elseif avgFPS >= 20 then
			return "Performance: POOR ⚠️"
		else
			return "Performance: CRITICAL ❌"
		end
	end
	function PerformanceMonitor:getPerformanceSummary()
		if #self.fpsHistory == 0 then
			return "No data"
		end
		local avgFPS = self:calculateAverage(self.fpsHistory)
		local avgFrameTime = self:calculateAverage(self.frameTimeHistory)
		return `FPS: {math.floor(avgFPS)} | Frame: {math.floor(avgFrameTime * 10) / 10}ms`
	end
	function PerformanceMonitor:isPerformanceGood()
		if #self.fpsHistory == 0 then
			return true
		end
		local avgFPS = self:calculateAverage(self.fpsHistory)
		return avgFPS >= 30
	end
	function PerformanceMonitor:getDetailedReport()
		if #self.fpsHistory == 0 then
			return nil
		end
		return {
			fps = self.fpsHistory[#self.fpsHistory],
			frameTime = self.frameTimeHistory[#self.frameTimeHistory],
			memoryUsage = self:getMemoryUsage(),
			networkReceive = 0,
			networkSend = 0,
			instanceCount = self:getInstanceCount(),
			heartbeatTime = self:getHeartbeatTime(),
		}
	end
	function PerformanceMonitor:cleanup()
		self.fpsHistory = {}
		self.frameTimeHistory = {}
	end
end
return {
	PerformanceMonitor = PerformanceMonitor,
}
