/**
 * Global Z-Index Management System
 * Automatically manages layering of UI elements so new windows always appear on top
 */

class ZIndexManagerClass {
    private currentZIndex = 1000; // Start at a high base value
    private registeredElements = new Map<string, number>();
    
    /**
     * Get the next available Z-Index for a new UI element
     * Each call returns a higher Z-Index, ensuring new elements appear on top
     */
    getNextZIndex(elementId?: string): number {
        this.currentZIndex += 10; // Increment by 10 to leave room for sub-elements
        
        if (elementId) {
            this.registeredElements.set(elementId, this.currentZIndex);
        }
        
        return this.currentZIndex;
    }
    
    /**
     * Get the current highest Z-Index without incrementing
     */
    getCurrentZIndex(): number {
        return this.currentZIndex;
    }
    
    /**
     * Bring an existing element to the front
     */
    bringToFront(elementId: string): number {
        const newZIndex = this.getNextZIndex(elementId);
        return newZIndex;
    }
    
    /**
     * Get the Z-Index for a specific element
     */
    getZIndex(elementId: string): number | undefined {
        return this.registeredElements.get(elementId);
    }
    
    /**
     * Remove an element from tracking
     */
    unregister(elementId: string): void {
        this.registeredElements.delete(elementId);
    }
    
    /**
     * Reset the Z-Index counter (useful for testing)
     */
    reset(): void {
        this.currentZIndex = 1000;
        this.registeredElements.clear();
    }
    
    /**
     * Get all registered elements and their Z-Indices
     */
    getRegisteredElements(): Map<string, number> {
        const copy = new Map<string, number>();
        this.registeredElements.forEach((value, key) => {
            copy.set(key, value);
        });
        return copy;
    }

    /**
     * Get a high-priority Z-Index for debug/system elements
     * These elements should always appear above regular UI
     */
    getDebugZIndex(elementId?: string): number {
        // Add extra increment to ensure debug elements are always on top
        this.currentZIndex += 50; // Large increment for debug priority

        if (elementId) {
            this.registeredElements.set(elementId, this.currentZIndex);
        }

        return this.currentZIndex;
    }
}

// Export a singleton instance
export const ZIndexManager = new ZIndexManagerClass();


